#ifndef ULN2003_DRIVER_H
#define ULN2003_DRIVER_H

#include "stm32f1xx_hal.h"

// 定义步进电机相位引脚
#define IN1_PIN GPIO_PIN_0
#define IN1_PORT GPIOA
#define IN2_PIN GPIO_PIN_1
#define IN2_PORT GPIOA
#define IN3_PIN GPIO_PIN_2
#define IN3_PORT GPIOA
#define IN4_PIN GPIO_PIN_3
#define IN4_PORT GPIOA

// 步进模式枚举
typedef enum {
    STEP_MODE_FULL,     // 全步模式
    STEP_MODE_HALF      // 半步模式
} StepMode;

// 方向枚举
typedef enum {
    DIRECTION_CW,       // 顺时针
    DIRECTION_CCW       // 逆时针
} Direction;

// 初始化函数
void ULN2003_Init(void);

// 设置步进模式
void ULN2003_SetMode(StepMode mode);

// 步进电机转动
void ULN2003_Step(Direction dir, uint16_t delay_ms);

// 停止电机
void ULN2003_Stop(void);

#endif // ULN2003_DRIVER_H