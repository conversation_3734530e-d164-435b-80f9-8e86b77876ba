#include "uln2003_driver.h"
#include "main.h"
#include "stm32f1xx_hal.h"

// 当前步进模式
static StepMode currentMode = STEP_MODE_FULL;

// 全步模式相位序列 (4步)
static const uint8_t fullStepSequence[4] = {
    0b0001, // IN1
    0b0010, // IN2
    0b0100, // IN3
    0b1000  // IN4
};

// 半步模式相位序列 (8步)
static const uint8_t halfStepSequence[8] = {
    0b0001, // IN1
    0b0011, // IN1 + IN2
    0b0010, // IN2
    0b0110, // IN2 + IN3
    0b0100, // IN3
    0b1100, // IN3 + IN4
    0b1000, // IN4
    0b1001  // IN4 + IN1
};

// 初始化ULN2003驱动板
void ULN2003_Init(void) {
    // GPIO已在CubeMX中初始化
    // 确保所有输出为低
    HAL_GPIO_WritePin(IN1_PORT, IN1_PIN, GPIO_PIN_RESET);
    HAL_GPIO_WritePin(IN2_PORT, IN2_PIN, GPIO_PIN_RESET);
    HAL_GPIO_WritePin(IN3_PORT, IN3_PIN, GPIO_PIN_RESET);
    HAL_GPIO_WritePin(IN4_PORT, IN4_PIN, GPIO_PIN_RESET);
}

// 设置步进模式
void ULN2003_SetMode(StepMode mode) {
    currentMode = mode;
}

// 步进电机转动
void ULN2003_Step(Direction dir, uint16_t delay_ms) {
    static uint8_t currentStep = 0;
    uint8_t phase;
    
    if(currentMode == STEP_MODE_FULL) {
        // 全步模式
        if(dir == DIRECTION_CW) {
            currentStep = (currentStep + 1) % 4;
        } else {
            currentStep = (currentStep - 1 + 4) % 4;
        }
        phase = fullStepSequence[currentStep];
    } else {
        // 半步模式
        if(dir == DIRECTION_CW) {
            currentStep = (currentStep + 1) % 8;
        } else {
            currentStep = (currentStep - 1 + 8) % 8;
        }
        phase = halfStepSequence[currentStep];
    }
    
    // 设置相位输出
    HAL_GPIO_WritePin(IN1_PORT, IN1_PIN, (phase & 0b0001) ? GPIO_PIN_SET : GPIO_PIN_RESET);
    HAL_GPIO_WritePin(IN2_PORT, IN2_PIN, (phase & 0b0010) ? GPIO_PIN_SET : GPIO_PIN_RESET);
    HAL_GPIO_WritePin(IN3_PORT, IN3_PIN, (phase & 0b0100) ? GPIO_PIN_SET : GPIO_PIN_RESET);
    HAL_GPIO_WritePin(IN4_PORT, IN4_PIN, (phase & 0b1000) ? GPIO_PIN_SET : GPIO_PIN_RESET);
    
    // 控制速度的延时
    HAL_Delay(delay_ms);
}

// 停止电机
void ULN2003_Stop(void) {
    // 关闭所有输出
    HAL_GPIO_WritePin(IN1_PORT, IN1_PIN, GPIO_PIN_RESET);
    HAL_GPIO_WritePin(IN2_PORT, IN2_PIN, GPIO_PIN_RESET);
    HAL_GPIO_WritePin(IN3_PORT, IN3_PIN, GPIO_PIN_RESET);
    HAL_GPIO_WritePin(IN4_PORT, IN4_PIN, GPIO_PIN_RESET);
}