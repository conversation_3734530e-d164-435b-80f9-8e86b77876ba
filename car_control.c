#include "car_control.h"
#include "main.h"

// 当前小车位置
static CarPosition currentPosition = POINT_A;

// 左右电机步进状态
static uint8_t leftMotorStep = 0;
static uint8_t rightMotorStep = 0;

// 全步模式相位序列
static const uint8_t stepSequence[4] = {
    0b0001, // IN1
    0b0010, // IN2
    0b0100, // IN3
    0b1000  // IN4
};

// 初始化小车系统
void Car_Init(void) {
    // 初始化所有电机引脚为低电平
    // 左侧电机
    HAL_GPIO_WritePin(LEFT_MOTOR_IN1_PORT, LEFT_MOTOR_IN1_PIN, GPIO_PIN_RESET);
    HAL_GPIO_WritePin(LEFT_MOTOR_IN2_PORT, LEFT_MOTOR_IN2_PIN, GPIO_PIN_RESET);
    HAL_GPIO_WritePin(LEFT_MOTOR_IN3_PORT, LEFT_MOTOR_IN3_PIN, GPIO_PIN_RESET);
    HAL_GPIO_WritePin(LEFT_MOTOR_IN4_PORT, LEFT_MOTOR_IN4_PIN, GPIO_PIN_RESET);
    
    // 右侧电机
    HAL_GPIO_WritePin(RIGHT_MOTOR_IN1_PORT, RIGHT_MOTOR_IN1_PIN, GPIO_PIN_RESET);
    HAL_GPIO_WritePin(RIGHT_MOTOR_IN2_PORT, RIGHT_MOTOR_IN2_PIN, GPIO_PIN_RESET);
    HAL_GPIO_WritePin(RIGHT_MOTOR_IN3_PORT, RIGHT_MOTOR_IN3_PIN, GPIO_PIN_RESET);
    HAL_GPIO_WritePin(RIGHT_MOTOR_IN4_PORT, RIGHT_MOTOR_IN4_PIN, GPIO_PIN_RESET);
    
    // 初始化声光提示引脚
    HAL_GPIO_WritePin(BUZZER_PORT, BUZZER_PIN, GPIO_PIN_RESET);
    HAL_GPIO_WritePin(LED_PORT, LED_PIN, GPIO_PIN_RESET);
    
    currentPosition = POINT_A;
}

// 左电机步进
void LeftMotor_Step(Direction dir) {
    uint8_t phase;
    
    if(dir == DIRECTION_CW) {
        leftMotorStep = (leftMotorStep + 1) % 4;
    } else {
        leftMotorStep = (leftMotorStep - 1 + 4) % 4;
    }
    
    phase = stepSequence[leftMotorStep];
    
    HAL_GPIO_WritePin(LEFT_MOTOR_IN1_PORT, LEFT_MOTOR_IN1_PIN, (phase & 0b0001) ? GPIO_PIN_SET : GPIO_PIN_RESET);
    HAL_GPIO_WritePin(LEFT_MOTOR_IN2_PORT, LEFT_MOTOR_IN2_PIN, (phase & 0b0010) ? GPIO_PIN_SET : GPIO_PIN_RESET);
    HAL_GPIO_WritePin(LEFT_MOTOR_IN3_PORT, LEFT_MOTOR_IN3_PIN, (phase & 0b0100) ? GPIO_PIN_SET : GPIO_PIN_RESET);
    HAL_GPIO_WritePin(LEFT_MOTOR_IN4_PORT, LEFT_MOTOR_IN4_PIN, (phase & 0b1000) ? GPIO_PIN_SET : GPIO_PIN_RESET);
}

// 右电机步进
void RightMotor_Step(Direction dir) {
    uint8_t phase;
    
    if(dir == DIRECTION_CW) {
        rightMotorStep = (rightMotorStep + 1) % 4;
    } else {
        rightMotorStep = (rightMotorStep - 1 + 4) % 4;
    }
    
    phase = stepSequence[rightMotorStep];
    
    HAL_GPIO_WritePin(RIGHT_MOTOR_IN1_PORT, RIGHT_MOTOR_IN1_PIN, (phase & 0b0001) ? GPIO_PIN_SET : GPIO_PIN_RESET);
    HAL_GPIO_WritePin(RIGHT_MOTOR_IN2_PORT, RIGHT_MOTOR_IN2_PIN, (phase & 0b0010) ? GPIO_PIN_SET : GPIO_PIN_RESET);
    HAL_GPIO_WritePin(RIGHT_MOTOR_IN3_PORT, RIGHT_MOTOR_IN3_PIN, (phase & 0b0100) ? GPIO_PIN_SET : GPIO_PIN_RESET);
    HAL_GPIO_WritePin(RIGHT_MOTOR_IN4_PORT, RIGHT_MOTOR_IN4_PIN, (phase & 0b1000) ? GPIO_PIN_SET : GPIO_PIN_RESET);
}

// 小车运动控制
void Car_Move(CarDirection direction, uint16_t steps) {
    for(uint16_t i = 0; i < steps; i++) {
        switch(direction) {
            case CAR_FORWARD:
                LeftMotor_Step(DIRECTION_CW);
                RightMotor_Step(DIRECTION_CW);
                break;
                
            case CAR_BACKWARD:
                LeftMotor_Step(DIRECTION_CCW);
                RightMotor_Step(DIRECTION_CCW);
                break;
                
            case CAR_LEFT:
                LeftMotor_Step(DIRECTION_CCW);
                RightMotor_Step(DIRECTION_CW);
                break;
                
            case CAR_RIGHT:
                LeftMotor_Step(DIRECTION_CW);
                RightMotor_Step(DIRECTION_CCW);
                break;
                
            case CAR_STOP:
            default:
                return;
        }
        HAL_Delay(STEP_DELAY_MS);
    }
}

// 停止小车
void Car_Stop(void) {
    // 关闭所有电机输出
    HAL_GPIO_WritePin(LEFT_MOTOR_IN1_PORT, LEFT_MOTOR_IN1_PIN, GPIO_PIN_RESET);
    HAL_GPIO_WritePin(LEFT_MOTOR_IN2_PORT, LEFT_MOTOR_IN2_PIN, GPIO_PIN_RESET);
    HAL_GPIO_WritePin(LEFT_MOTOR_IN3_PORT, LEFT_MOTOR_IN3_PIN, GPIO_PIN_RESET);
    HAL_GPIO_WritePin(LEFT_MOTOR_IN4_PORT, LEFT_MOTOR_IN4_PIN, GPIO_PIN_RESET);
    
    HAL_GPIO_WritePin(RIGHT_MOTOR_IN1_PORT, RIGHT_MOTOR_IN1_PIN, GPIO_PIN_RESET);
    HAL_GPIO_WritePin(RIGHT_MOTOR_IN2_PORT, RIGHT_MOTOR_IN2_PIN, GPIO_PIN_RESET);
    HAL_GPIO_WritePin(RIGHT_MOTOR_IN3_PORT, RIGHT_MOTOR_IN3_PIN, GPIO_PIN_RESET);
    HAL_GPIO_WritePin(RIGHT_MOTOR_IN4_PORT, RIGHT_MOTOR_IN4_PIN, GPIO_PIN_RESET);
}

// 左转
void Car_TurnLeft(void) {
    Car_Move(CAR_LEFT, TURN_STEPS);
}

// 右转
void Car_TurnRight(void) {
    Car_Move(CAR_RIGHT, TURN_STEPS);
}

// 前进
void Car_Forward(uint16_t distance_units) {
    Car_Move(CAR_FORWARD, distance_units * FORWARD_STEPS_PER_UNIT);
}

// 后退
void Car_Backward(uint16_t distance_units) {
    Car_Move(CAR_BACKWARD, distance_units * FORWARD_STEPS_PER_UNIT);
}

// 声光提示
void Sound_Light_Alert(void) {
    // 同时开启蜂鸣器和LED
    HAL_GPIO_WritePin(BUZZER_PORT, BUZZER_PIN, GPIO_PIN_SET);
    HAL_GPIO_WritePin(LED_PORT, LED_PIN, GPIO_PIN_SET);
    HAL_Delay(500);
    
    // 关闭蜂鸣器和LED
    HAL_GPIO_WritePin(BUZZER_PORT, BUZZER_PIN, GPIO_PIN_RESET);
    HAL_GPIO_WritePin(LED_PORT, LED_PIN, GPIO_PIN_RESET);
    HAL_Delay(200);
}

// 蜂鸣器响
void Buzzer_Beep(uint16_t duration_ms) {
    HAL_GPIO_WritePin(BUZZER_PORT, BUZZER_PIN, GPIO_PIN_SET);
    HAL_Delay(duration_ms);
    HAL_GPIO_WritePin(BUZZER_PORT, BUZZER_PIN, GPIO_PIN_RESET);
}

// LED闪烁
void LED_Blink(uint16_t duration_ms) {
    HAL_GPIO_WritePin(LED_PORT, LED_PIN, GPIO_PIN_SET);
    HAL_Delay(duration_ms);
    HAL_GPIO_WritePin(LED_PORT, LED_PIN, GPIO_PIN_RESET);
}

// 按键扫描
uint8_t Key_Scan(void) {
    if(HAL_GPIO_ReadPin(KEY1_PORT, KEY1_PIN) == GPIO_PIN_RESET) {
        HAL_Delay(20); // 消抖
        if(HAL_GPIO_ReadPin(KEY1_PORT, KEY1_PIN) == GPIO_PIN_RESET) {
            while(HAL_GPIO_ReadPin(KEY1_PORT, KEY1_PIN) == GPIO_PIN_RESET); // 等待释放
            return 1;
        }
    }
    
    if(HAL_GPIO_ReadPin(KEY2_PORT, KEY2_PIN) == GPIO_PIN_RESET) {
        HAL_Delay(20);
        if(HAL_GPIO_ReadPin(KEY2_PORT, KEY2_PIN) == GPIO_PIN_RESET) {
            while(HAL_GPIO_ReadPin(KEY2_PORT, KEY2_PIN) == GPIO_PIN_RESET);
            return 2;
        }
    }
    
    if(HAL_GPIO_ReadPin(KEY3_PORT, KEY3_PIN) == GPIO_PIN_RESET) {
        HAL_Delay(20);
        if(HAL_GPIO_ReadPin(KEY3_PORT, KEY3_PIN) == GPIO_PIN_RESET) {
            while(HAL_GPIO_ReadPin(KEY3_PORT, KEY3_PIN) == GPIO_PIN_RESET);
            return 3;
        }
    }
    
    if(HAL_GPIO_ReadPin(KEY4_PORT, KEY4_PIN) == GPIO_PIN_RESET) {
        HAL_Delay(20);
        if(HAL_GPIO_ReadPin(KEY4_PORT, KEY4_PIN) == GPIO_PIN_RESET) {
            while(HAL_GPIO_ReadPin(KEY4_PORT, KEY4_PIN) == GPIO_PIN_RESET);
            return 4;
        }
    }
    
    return 0;
}

// 从一个点移动到另一个点
void Car_GoToPoint(CarPosition from, CarPosition to) {
    // 简化的路径规划：假设A、B、C、D四个点构成一个矩形
    // A在左下角，B在右下角，C在右上角，D在左上角

    if(from == to) {
        return; // 已经在目标点
    }

    switch(from) {
        case POINT_A:
            if(to == POINT_B) {
                Car_Forward(2); // A到B：向右前进
            } else if(to == POINT_C) {
                Car_Forward(2); // A到C：先向右
                Car_TurnLeft(); // 左转
                Car_Forward(2); // 向上
            } else if(to == POINT_D) {
                Car_TurnLeft(); // A到D：左转
                Car_Forward(2); // 向上前进
            }
            break;

        case POINT_B:
            if(to == POINT_A) {
                Car_Backward(2); // B到A：向左后退
            } else if(to == POINT_C) {
                Car_TurnLeft(); // B到C：左转
                Car_Forward(2); // 向上前进
            } else if(to == POINT_D) {
                Car_TurnLeft(); // B到D：左转
                Car_Forward(2); // 向上
                Car_TurnLeft(); // 再左转
                Car_Forward(2); // 向左
            }
            break;

        case POINT_C:
            if(to == POINT_A) {
                Car_Backward(2); // C到A：向下
                Car_TurnRight(); // 右转
                Car_Backward(2); // 向左
            } else if(to == POINT_B) {
                Car_TurnRight(); // C到B：右转
                Car_Forward(2); // 向下前进
            } else if(to == POINT_D) {
                Car_Backward(2); // C到D：向左后退
            }
            break;

        case POINT_D:
            if(to == POINT_A) {
                Car_TurnRight(); // D到A：右转
                Car_Forward(2); // 向下前进
            } else if(to == POINT_B) {
                Car_Forward(2); // D到B：向右
                Car_TurnRight(); // 右转
                Car_Forward(2); // 向下
            } else if(to == POINT_C) {
                Car_Forward(2); // D到C：向右前进
            }
            break;
    }

    currentPosition = to;
    Sound_Light_Alert(); // 到达目标点时声光提示
}

// 执行指定任务
void Car_ExecuteTask(TaskMode task) {
    uint32_t startTime = HAL_GetTick();

    switch(task) {
        case TASK_1_A_TO_B:
            // 任务1：从A点到B点停车，用时不大于15秒
            currentPosition = POINT_A;
            Car_GoToPoint(POINT_A, POINT_B);
            Car_Stop();
            Sound_Light_Alert(); // 停车时声光提示
            break;

        case TASK_2_ABCDA:
            // 任务2：A→B→C→D→A循环，完成一圈用时不大于30秒
            currentPosition = POINT_A;
            Car_GoToPoint(POINT_A, POINT_B);
            Car_GoToPoint(POINT_B, POINT_C);
            Car_GoToPoint(POINT_C, POINT_D);
            Car_GoToPoint(POINT_D, POINT_A);
            Car_Stop();
            break;

        case TASK_3_ACBDA:
            // 任务3：A→C→B→D→A循环，完成一圈用时不大于40秒
            currentPosition = POINT_A;
            Car_GoToPoint(POINT_A, POINT_C);
            Car_GoToPoint(POINT_C, POINT_B);
            Car_GoToPoint(POINT_B, POINT_D);
            Car_GoToPoint(POINT_D, POINT_A);
            Car_Stop();
            break;

        case TASK_4_ACBDA_4LOOPS:
            // 任务4：按要求3的路径自动行驶4圈停车
            currentPosition = POINT_A;
            for(int loop = 0; loop < 4; loop++) {
                Car_GoToPoint(POINT_A, POINT_C);
                Car_GoToPoint(POINT_C, POINT_B);
                Car_GoToPoint(POINT_B, POINT_D);
                Car_GoToPoint(POINT_D, POINT_A);
            }
            Car_Stop();
            Sound_Light_Alert(); // 完成4圈后声光提示
            break;
    }

    uint32_t endTime = HAL_GetTick();
    uint32_t elapsedTime = endTime - startTime;

    // 可以在这里添加时间检查和优化逻辑
    // 例如：如果时间超出要求，可以调整运动参数
}
