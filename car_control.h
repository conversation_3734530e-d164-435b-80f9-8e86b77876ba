#ifndef CAR_CONTROL_H
#define CAR_CONTROL_H

#include "stm32f1xx_hal.h"
#include "uln2003_driver.h"

// 小车位置点枚举
typedef enum {
    POINT_A = 0,
    POINT_B = 1,
    POINT_C = 2,
    POINT_D = 3
} CarPosition;

// 小车运动方向
typedef enum {
    CAR_FORWARD,
    CAR_BACKWARD,
    CAR_LEFT,
    CAR_RIGHT,
    CAR_STOP
} CarDirection;

// 任务模式
typedef enum {
    TASK_1_A_TO_B = 1,      // 任务1：A到B
    TASK_2_ABCDA = 2,       // 任务2：A→B→C→D→A
    TASK_3_ACBDA = 3,       // 任务3：A→C→B→D→A
    TASK_4_ACBDA_4LOOPS = 4 // 任务4：A→C→B→D→A 4圈
} TaskMode;

// 电机引脚定义 - 左侧电机
#define LEFT_MOTOR_IN1_PIN GPIO_PIN_0
#define LEFT_MOTOR_IN1_PORT GPIOA
#define LEFT_MOTOR_IN2_PIN GPIO_PIN_1
#define LEFT_MOTOR_IN2_PORT GPIOA
#define LEFT_MOTOR_IN3_PIN GPIO_PIN_2
#define LEFT_MOTOR_IN3_PORT GPIOA
#define LEFT_MOTOR_IN4_PIN GPIO_PIN_3
#define LEFT_MOTOR_IN4_PORT GPIOA

// 电机引脚定义 - 右侧电机
#define RIGHT_MOTOR_IN1_PIN GPIO_PIN_4
#define RIGHT_MOTOR_IN1_PORT GPIOA
#define RIGHT_MOTOR_IN2_PIN GPIO_PIN_5
#define RIGHT_MOTOR_IN2_PORT GPIOA
#define RIGHT_MOTOR_IN3_PIN GPIO_PIN_6
#define RIGHT_MOTOR_IN3_PORT GPIOA
#define RIGHT_MOTOR_IN4_PIN GPIO_PIN_7
#define RIGHT_MOTOR_IN4_PORT GPIOA

// 运动参数
#define STEPS_PER_REVOLUTION 512    // 全步模式每圈步数
#define STEP_DELAY_MS 5            // 步进延时
#define TURN_STEPS 128             // 转弯所需步数
#define FORWARD_STEPS_PER_UNIT 256 // 前进单位距离步数

// 函数声明
void Car_Init(void);
void Car_Move(CarDirection direction, uint16_t steps);
void Car_Stop(void);
void Car_TurnLeft(void);
void Car_TurnRight(void);
void Car_Forward(uint16_t distance_units);
void Car_Backward(uint16_t distance_units);

// 路径控制函数
void Car_GoToPoint(CarPosition from, CarPosition to);
void Car_ExecuteTask(TaskMode task);

// 声光提示函数
void Sound_Light_Alert(void);
void Buzzer_Beep(uint16_t duration_ms);
void LED_Blink(uint16_t duration_ms);

// 按键检测函数
uint8_t Key_Scan(void);

#endif // CAR_CONTROL_H
