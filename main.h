#ifndef MAIN_H
#define MAIN_H

#include "stm32f1xx_hal.h"

// 声光提示引脚定义
#define BUZZER_PIN GPIO_PIN_8
#define BUZZER_PORT GPIOB
#define LED_PIN GPIO_PIN_9
#define LED_PORT GPIOB

// 按键引脚定义
#define KEY1_PIN GPIO_PIN_0
#define KEY1_PORT GPIOB
#define KEY2_PIN GPIO_PIN_1
#define KEY2_PORT GPIOB
#define KEY3_PIN GPIO_PIN_2
#define KEY3_PORT GPIOB
#define KEY4_PIN GPIO_PIN_3
#define KEY4_PORT GPIOB

// 系统函数声明
void SystemClock_Config(void);
void GPIO_Init(void);
void Error_Handler(void);

#endif // MAIN_H
