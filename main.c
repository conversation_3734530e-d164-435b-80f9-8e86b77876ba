#include "main.h"
#include "uln2003_driver.h"

// 系统时钟配置（由CubeMX生成）
void SystemClock_Config(void);

int main(void) {
    // HAL库初始化
    HAL_Init();
    SystemClock_Config();
    
    // 初始化ULN2003驱动板
    ULN2003_Init();
    
    // 设置步进模式为全步
    ULN2003_SetMode(STEP_MODE_FULL);
    
    // 示例1：顺时针旋转10圈（全步模式，每步5ms）
    for(int i = 0; i < 512 * 10; i++) { // 28BYJ-48全步模式每圈512步
        ULN2003_Step(DIRECTION_CW, 5);
    }
    
    // 停止1秒
    ULN2003_Stop();
    HAL_Delay(1000);
    
    // 设置步进模式为半步
    ULN2003_SetMode(STEP_MODE_HALF);
    
    // 示例2：逆时针旋转5圈（半步模式，每步3ms）
    for(int i = 0; i < 1024 * 5; i++) { // 28BYJ-48半步模式每圈1024步
        ULN2003_Step(DIRECTION_CCW, 3);
    }
    
    // 停止电机
    ULN2003_Stop();
    
    while(1) {
        // 主循环
    }
}

// 以下函数通常由CubeMX自动生成
void SystemClock_Config(void) {
    // 这里应该有具体的时钟配置代码
    // 实际项目中这部分由CubeMX自动生成
}