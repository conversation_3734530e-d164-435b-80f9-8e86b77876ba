#include "main.h"
#include "uln2003_driver.h"
#include "car_control.h"

// 系统时钟配置（由CubeMX生成）
void SystemClock_Config(void);
void GPIO_Init(void);

int main(void) {
    // HAL库初始化
    HAL_Init();
    SystemClock_Config();
    GPIO_Init();

    // 初始化小车控制系统
    Car_Init();

    uint8_t keyPressed = 0;
    uint8_t currentTask = 0;

    // 开机声光提示
    Sound_Light_Alert();
    HAL_Delay(1000);

    while(1) {
        // 扫描按键
        keyPressed = Key_Scan();

        if(keyPressed != 0 && keyPressed != currentTask) {
            currentTask = keyPressed;

            // 执行对应任务
            switch(keyPressed) {
                case 1:
                    // 任务1：A到B点停车，用时不大于15秒
                    Car_ExecuteTask(TASK_1_A_TO_B);
                    break;

                case 2:
                    // 任务2：A→B→C→D→A循环，完成一圈用时不大于30秒
                    Car_ExecuteTask(TASK_2_ABCDA);
                    break;

                case 3:
                    // 任务3：A→C→B→D→A循环，完成一圈用时不大于40秒
                    Car_ExecuteTask(TASK_3_ACBDA);
                    break;

                case 4:
                    // 任务4：按要求3的路径自动行驶4圈停车
                    Car_ExecuteTask(TASK_4_ACBDA_4LOOPS);
                    break;

                default:
                    break;
            }

            // 任务完成后的提示
            for(int i = 0; i < 3; i++) {
                Sound_Light_Alert();
                HAL_Delay(300);
            }
        }

        HAL_Delay(50); // 主循环延时
    }
}

// GPIO初始化函数
void GPIO_Init(void) {
    GPIO_InitTypeDef GPIO_InitStruct = {0};

    // 使能GPIO时钟
    __HAL_RCC_GPIOA_CLK_ENABLE();
    __HAL_RCC_GPIOB_CLK_ENABLE();

    // 配置电机控制引脚 - 左侧电机
    GPIO_InitStruct.Pin = LEFT_MOTOR_IN1_PIN | LEFT_MOTOR_IN2_PIN |
                         LEFT_MOTOR_IN3_PIN | LEFT_MOTOR_IN4_PIN;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
    HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);

    // 配置电机控制引脚 - 右侧电机
    GPIO_InitStruct.Pin = RIGHT_MOTOR_IN1_PIN | RIGHT_MOTOR_IN2_PIN |
                         RIGHT_MOTOR_IN3_PIN | RIGHT_MOTOR_IN4_PIN;
    HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);

    // 配置声光提示引脚
    GPIO_InitStruct.Pin = BUZZER_PIN | LED_PIN;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
    HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);

    // 配置按键引脚
    GPIO_InitStruct.Pin = KEY1_PIN | KEY2_PIN | KEY3_PIN | KEY4_PIN;
    GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
    GPIO_InitStruct.Pull = GPIO_PULLUP;
    HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);
}

// 系统时钟配置（由CubeMX生成）
void SystemClock_Config(void) {
    RCC_OscInitTypeDef RCC_OscInitStruct = {0};
    RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};

    // 配置内部高速振荡器HSI
    RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSI;
    RCC_OscInitStruct.HSIState = RCC_HSI_ON;
    RCC_OscInitStruct.HSICalibrationValue = RCC_HSICALIBRATION_DEFAULT;
    RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
    RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSI_DIV2;
    RCC_OscInitStruct.PLL.PLLMUL = RCC_PLL_MUL16;
    if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK) {
        Error_Handler();
    }

    // 配置系统时钟
    RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
                                |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2;
    RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
    RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
    RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV2;
    RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV1;

    if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_2) != HAL_OK) {
        Error_Handler();
    }
}

// 错误处理函数
void Error_Handler(void) {
    // 用户可以在这里添加自己的错误处理代码
    __disable_irq();
    while (1) {
        // 错误时LED快速闪烁
        HAL_GPIO_TogglePin(LED_PORT, LED_PIN);
        HAL_Delay(100);
    }
}